#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/bin/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/jiti@1.21.7/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/bin/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/jiti@1.21.7/node_modules/jiti/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/jiti@1.21.7/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../jiti/bin/jiti.js" "$@"
else
  exec node  "$basedir/../../../jiti/bin/jiti.js" "$@"
fi
