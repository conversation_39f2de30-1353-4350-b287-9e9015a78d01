#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite@6.3.5_@types+node@20.1_fd4a8fa7ec3f19dbe60a27aedb7f7fbe/node_modules/vite/bin/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite@6.3.5_@types+node@20.1_fd4a8fa7ec3f19dbe60a27aedb7f7fbe/node_modules/vite/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite@6.3.5_@types+node@20.1_fd4a8fa7ec3f19dbe60a27aedb7f7fbe/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite@6.3.5_@types+node@20.1_fd4a8fa7ec3f19dbe60a27aedb7f7fbe/node_modules/vite/bin/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite@6.3.5_@types+node@20.1_fd4a8fa7ec3f19dbe60a27aedb7f7fbe/node_modules/vite/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite@6.3.5_@types+node@20.1_fd4a8fa7ec3f19dbe60a27aedb7f7fbe/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vite/bin/vite.js" "$@"
else
  exec node  "$basedir/../../../vite/bin/vite.js" "$@"
fi
