#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/mnt/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite-node@3.2.2_@types+node_c3237395bd1c118cc3979147086b0e3d/node_modules/vite-node/node_modules:/mnt/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite-node@3.2.2_@types+node_c3237395bd1c118cc3979147086b0e3d/node_modules:/mnt/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/mnt/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite-node@3.2.2_@types+node_c3237395bd1c118cc3979147086b0e3d/node_modules/vite-node/node_modules:/mnt/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vite-node@3.2.2_@types+node_c3237395bd1c118cc3979147086b0e3d/node_modules:/mnt/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../../../vite-node/vite-node.mjs" "$@"
else
  exec node  "$basedir/../../../vite-node/vite-node.mjs" "$@"
fi
