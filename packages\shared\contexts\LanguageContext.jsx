import React, { createContext, useContext, useState } from 'react';

const LanguageContext = createContext();

const translations = {
  en: {
    // Navigation
    home: 'Home',
    marketplace: 'Marketplace',
    aiAnalysis: 'AI Analysis',
    logistics: 'Logistics',
    demandPortal: 'Demand Portal',
    farmerDashboard: 'Farmer Dashboard',
    buyerDashboard: 'Buyer Dashboard',
    subscription: 'Subscription',
    
    // Hero Section
    heroTitle: 'Transforming Agriculture in the Democratic Republic of Congo',
    heroSubtitle: 'Empowering local farmers through modern agricultural technologies, technical assistance, and direct market connections in the Kivu region.',
    getStarted: 'Get Started',
    learnMore: 'Learn More',
    
    // Features
    featuresTitle: 'Revolutionary Features',
    marketLinkage: 'Market Linkage Portal',
    marketLinkageDesc: 'Connect farmers directly with buyers - hotels, restaurants, and households for fair pricing.',
    aiVision: 'AI + Computer Vision',
    aiVisionDesc: 'Upload crop images for automated pest/disease detection and treatment recommendations.',
    supplyChain: 'Supply Chain Tracking',
    supplyChainDesc: 'End-to-end visibility for buyers on crop quality and source origin.',
    logisticsCoordination: 'Logistics Coordination',
    logisticsDesc: 'Manage transportation and storage through integrated scheduling and tracking.',
    
    // Stats
    farmersConnected: 'Farmers Connected',
    buyersActive: 'Active Buyers',
    cropsAnalyzed: 'Crops Analyzed',
    successRate: 'Success Rate',
    
    // Common
    login: 'Login',
    signup: 'Sign Up',
    dashboard: 'Dashboard',
    profile: 'Profile',
    settings: 'Settings',
    logout: 'Logout',
    save: 'Save',
    cancel: 'Cancel',
    edit: 'Edit',
    delete: 'Delete',
    view: 'View',
    upload: 'Upload',
    download: 'Download',
    search: 'Search',
    filter: 'Filter',
    sort: 'Sort',
    loading: 'Loading...',
    error: 'Error',
    success: 'Success',
    warning: 'Warning',
    info: 'Information'
  },
  fr: {
    // Navigation
    home: 'Accueil',
    marketplace: 'Marché',
    aiAnalysis: 'Analyse IA',
    logistics: 'Logistique',
    demandPortal: 'Portail de Demande',
    farmerDashboard: 'Tableau de Bord Agriculteur',
    buyerDashboard: 'Tableau de Bord Acheteur',
    subscription: 'Abonnement',
    
    // Hero Section
    heroTitle: 'Transformer l\'Agriculture en République Démocratique du Congo',
    heroSubtitle: 'Autonomiser les agriculteurs locaux grâce aux technologies agricoles modernes, à l\'assistance technique et aux connexions directes au marché dans la région du Kivu.',
    getStarted: 'Commencer',
    learnMore: 'En Savoir Plus',
    
    // Features
    featuresTitle: 'Fonctionnalités Révolutionnaires',
    marketLinkage: 'Portail de Liaison Marché',
    marketLinkageDesc: 'Connecter directement les agriculteurs aux acheteurs - hôtels, restaurants et ménages pour des prix équitables.',
    aiVision: 'IA + Vision par Ordinateur',
    aiVisionDesc: 'Téléchargez des images de cultures pour la détection automatisée des ravageurs/maladies et des recommandations de traitement.',
    supplyChain: 'Suivi de la Chaîne d\'Approvisionnement',
    supplyChainDesc: 'Visibilité de bout en bout pour les acheteurs sur la qualité des cultures et l\'origine de la source.',
    logisticsCoordination: 'Coordination Logistique',
    logisticsDesc: 'Gérer le transport et le stockage grâce à la planification et au suivi intégrés.',
    
    // Stats
    farmersConnected: 'Agriculteurs Connectés',
    buyersActive: 'Acheteurs Actifs',
    cropsAnalyzed: 'Cultures Analysées',
    successRate: 'Taux de Réussite',
    
    // Common
    login: 'Connexion',
    signup: 'S\'inscrire',
    dashboard: 'Tableau de Bord',
    profile: 'Profil',
    settings: 'Paramètres',
    logout: 'Déconnexion',
    save: 'Enregistrer',
    cancel: 'Annuler',
    edit: 'Modifier',
    delete: 'Supprimer',
    view: 'Voir',
    upload: 'Télécharger',
    download: 'Télécharger',
    search: 'Rechercher',
    filter: 'Filtrer',
    sort: 'Trier',
    loading: 'Chargement...',
    error: 'Erreur',
    success: 'Succès',
    warning: 'Avertissement',
    info: 'Information'
  },
  sw: {
    // Navigation
    home: 'Nyumbani',
    marketplace: 'Soko',
    aiAnalysis: 'Uchambuzi wa AI',
    logistics: 'Usafirishaji',
    demandPortal: 'Mlango wa Mahitaji',
    farmerDashboard: 'Dashibodi ya Mkulima',
    buyerDashboard: 'Dashibodi ya Mnunuzi',
    subscription: 'Usajili',
    
    // Hero Section
    heroTitle: 'Kubadilisha Kilimo katika Jamhuri ya Kidemokrasia ya Kongo',
    heroSubtitle: 'Kuwawezesha wakulima wa ndani kupitia teknolojia za kisasa za kilimo, msaada wa kiufundi, na miunganisho ya moja kwa moja ya soko katika mkoa wa Kivu.',
    getStarted: 'Anza',
    learnMore: 'Jifunze Zaidi',
    
    // Features
    featuresTitle: 'Vipengele vya Mapinduzi',
    marketLinkage: 'Mlango wa Kuunganisha Soko',
    marketLinkageDesc: 'Unganisha wakulima moja kwa moja na wanunuzi - hoteli, migahawa, na makazi kwa bei za haki.',
    aiVision: 'AI + Miwani ya Kompyuta',
    aiVisionDesc: 'Pakia picha za mazao kwa utambuzi wa otomatiki wa wadudu/magonjwa na mapendekezo ya matibabu.',
    supplyChain: 'Ufuatiliaji wa Mlolongo wa Ugavi',
    supplyChainDesc: 'Mwonekano wa mwisho hadi mwisho kwa wanunuzi juu ya ubora wa mazao na asili ya chanzo.',
    logisticsCoordination: 'Uratibu wa Usafirishaji',
    logisticsDesc: 'Dhibiti usafirishaji na uhifadhi kupitia mipango na ufuatiliaji uliounganishwa.',
    
    // Stats
    farmersConnected: 'Wakulima Walioungana',
    buyersActive: 'Wanunuzi Hai',
    cropsAnalyzed: 'Mazao Yalichambuliwa',
    successRate: 'Kiwango cha Mafanikio',
    
    // Common
    login: 'Ingia',
    signup: 'Jisajili',
    dashboard: 'Dashibodi',
    profile: 'Wasifu',
    settings: 'Mipangilio',
    logout: 'Toka',
    save: 'Hifadhi',
    cancel: 'Ghairi',
    edit: 'Hariri',
    delete: 'Futa',
    view: 'Ona',
    upload: 'Pakia',
    download: 'Pakua',
    search: 'Tafuta',
    filter: 'Chuja',
    sort: 'Panga',
    loading: 'Inapakia...',
    error: 'Hitilafu',
    success: 'Mafanikio',
    warning: 'Onyo',
    info: 'Taarifa'
  }
};

export const LanguageProvider = ({ children }) => {
  const [language, setLanguage] = useState('en');

  const t = (key, fallback = '') => {
    if (!translations[language] || !translations[language][key]) {
      console.warn(`Translation missing for key: ${key}`);
      return fallback || key;
    }
    return translations[language][key];
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (!context) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};
