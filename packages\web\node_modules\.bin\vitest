#!/bin/sh
basedir=$(dirname "$(echo "$0" | sed -e 's,\\,/,g')")

case `uname` in
    *CYGWIN*|*MINGW*|*MSYS*)
        if command -v cygpath > /dev/null 2>&1; then
            basedir=`cygpath -w "$basedir"`
        fi
    ;;
esac

if [ -z "$NODE_PATH" ]; then
  export NODE_PATH="/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b/node_modules/vitest/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules"
else
  export NODE_PATH="/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b/node_modules/vitest/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules:$NODE_PATH"
fi
if [ -x "$basedir/node" ]; then
  exec "$basedir/node"  "$basedir/../vitest/vitest.mjs" "$@"
else
  exec node  "$basedir/../vitest/vitest.mjs" "$@"
fi
