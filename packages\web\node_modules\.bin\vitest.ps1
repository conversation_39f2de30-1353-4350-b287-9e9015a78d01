#!/usr/bin/env pwsh
$basedir=Split-Path $MyInvocation.MyCommand.Definition -Parent

$exe=""
$pathsep=":"
$env_node_path=$env:NODE_PATH
$new_node_path="C:\My Software Projects\kivusmartfarm\node_modules\.pnpm\vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b\node_modules\vitest\node_modules;C:\My Software Projects\kivusmartfarm\node_modules\.pnpm\vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b\node_modules;C:\My Software Projects\kivusmartfarm\node_modules\.pnpm\node_modules"
if ($PSVersionTable.PSVersion -lt "6.0" -or $IsWindows) {
  # Fix case when both the Windows and Linux builds of Node
  # are installed in the same directory
  $exe=".exe"
  $pathsep=";"
} else {
  $new_node_path="/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b/node_modules/vitest/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/vitest@3.2.2_@types+node@20_eb0e7f353e936929de0d0f242537e46b/node_modules:/proc/cygdrive/c/My Software Projects/kivusmartfarm/node_modules/.pnpm/node_modules"
}
if ([string]::IsNullOrEmpty($env_node_path)) {
  $env:NODE_PATH=$new_node_path
} else {
  $env:NODE_PATH="$new_node_path$pathsep$env_node_path"
}

$ret=0
if (Test-Path "$basedir/node$exe") {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "$basedir/node$exe"  "$basedir/../vitest/vitest.mjs" $args
  } else {
    & "$basedir/node$exe"  "$basedir/../vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
} else {
  # Support pipeline input
  if ($MyInvocation.ExpectingInput) {
    $input | & "node$exe"  "$basedir/../vitest/vitest.mjs" $args
  } else {
    & "node$exe"  "$basedir/../vitest/vitest.mjs" $args
  }
  $ret=$LASTEXITCODE
}
$env:NODE_PATH=$env_node_path
exit $ret
