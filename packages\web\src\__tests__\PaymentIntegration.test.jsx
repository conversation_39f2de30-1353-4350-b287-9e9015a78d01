import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { CartProvider, LanguageProvider } from '@kivu-smartfarm/shared';
import MobileMoneyPayment from '../components/payments/MobileMoneyPayment';
import CardPayment from '../components/payments/CardPayment';
import CashOnDeliveryPayment from '../components/payments/CashOnDeliveryPayment';
import PayPalPayment from '../components/payments/PayPalPayment';

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <LanguageProvider>
        <CartProvider>
          {component}
        </CartProvider>
      </LanguageProvider>
    </BrowserRouter>
  );
};

const mockCustomerInfo = {
  name: '<PERSON>',
  email: '<EMAIL>',
  phone: '+243123456789',
  address: '123 Test Street',
  city: 'Goma',
  postalCode: '12345'
};

describe('Payment Integration', () => {
  const mockOnSuccess = vi.fn();
  const mockOnError = vi.fn();

  beforeEach(() => {
    vi.clearAllMocks();
  });

  describe('Mobile Money Payment', () => {
    test('should render mobile money payment form', () => {
      renderWithProviders(
        <MobileMoneyPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      expect(screen.getByText('Mobile Money Payment')).toBeInTheDocument();
      expect(screen.getByLabelText('Mobile Number')).toBeInTheDocument();
      expect(screen.getByText('$25.50')).toBeInTheDocument();
    });

    test('should validate mobile number input', async () => {
      renderWithProviders(
        <MobileMoneyPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      const payButton = screen.getByText('Pay Now');
      fireEvent.click(payButton);

      await waitFor(() => {
        expect(screen.getByText(/Mobile number is required/)).toBeInTheDocument();
      });
    });

    test('should process successful payment', async () => {
      renderWithProviders(
        <MobileMoneyPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      const mobileInput = screen.getByLabelText('Mobile Number');
      const payButton = screen.getByText('Pay Now');

      fireEvent.change(mobileInput, { target: { value: '+243123456789' } });
      fireEvent.click(payButton);

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            success: true,
            method: 'mobile-money',
            amount: 25.50
          })
        );
      }, { timeout: 3000 });
    });
  });

  describe('Card Payment', () => {
    test('should render card payment form', () => {
      renderWithProviders(
        <CardPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      expect(screen.getByText('Credit/Debit Card Payment')).toBeInTheDocument();
      expect(screen.getByLabelText('Cardholder Name')).toBeInTheDocument();
      expect(screen.getByLabelText('Card Number')).toBeInTheDocument();
      expect(screen.getByLabelText('Expiry Date')).toBeInTheDocument();
      expect(screen.getByLabelText('CVV')).toBeInTheDocument();
    });

    test('should validate card information', async () => {
      renderWithProviders(
        <CardPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      const payButton = screen.getByText('Pay Now');
      fireEvent.click(payButton);

      await waitFor(() => {
        expect(screen.getByText(/Cardholder name is required/)).toBeInTheDocument();
      });
    });

    test('should format card number correctly', () => {
      renderWithProviders(
        <CardPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      const cardNumberInput = screen.getByLabelText('Card Number');
      fireEvent.change(cardNumberInput, { target: { value: '1234567890123456' } });

      expect(cardNumberInput.value).toBe('1234 5678 9012 3456');
    });

    test('should process successful card payment', async () => {
      renderWithProviders(
        <CardPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      // Fill in all required fields
      fireEvent.change(screen.getByLabelText('Cardholder Name'), { 
        target: { value: 'John Doe' } 
      });
      fireEvent.change(screen.getByLabelText('Card Number'), { 
        target: { value: '1234567890123456' } 
      });
      fireEvent.change(screen.getByLabelText('Expiry Date'), { 
        target: { value: '12/25' } 
      });
      fireEvent.change(screen.getByLabelText('CVV'), { 
        target: { value: '123' } 
      });

      const payButton = screen.getByText('Pay Now');
      fireEvent.click(payButton);

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            success: true,
            method: 'card',
            amount: 25.50
          })
        );
      }, { timeout: 3000 });
    });
  });

  describe('Cash on Delivery Payment', () => {
    test('should render cash on delivery information', () => {
      renderWithProviders(
        <CashOnDeliveryPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      expect(screen.getByText('Cash on Delivery')).toBeInTheDocument();
      expect(screen.getByText('John Doe')).toBeInTheDocument();
      expect(screen.getByText('123 Test Street')).toBeInTheDocument();
      expect(screen.getByText('$25.50')).toBeInTheDocument();
    });

    test('should process cash on delivery order', async () => {
      renderWithProviders(
        <CashOnDeliveryPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      const confirmButton = screen.getByText('Confirm Order');
      fireEvent.click(confirmButton);

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            success: true,
            method: 'cash-on-delivery',
            amount: 25.50
          })
        );
      }, { timeout: 2000 });
    });
  });

  describe('PayPal Payment', () => {
    test('should render PayPal payment information', () => {
      renderWithProviders(
        <PayPalPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      expect(screen.getByText('PayPal Payment')).toBeInTheDocument();
      expect(screen.getByText('Pay with PayPal')).toBeInTheDocument();
      expect(screen.getByText('$25.50')).toBeInTheDocument();
    });

    test('should process PayPal payment', async () => {
      renderWithProviders(
        <PayPalPayment
          amount={25.50}
          onSuccess={mockOnSuccess}
          onError={mockOnError}
          customerInfo={mockCustomerInfo}
        />
      );

      const payButton = screen.getByText('Pay with PayPal');
      fireEvent.click(payButton);

      await waitFor(() => {
        expect(mockOnSuccess).toHaveBeenCalledWith(
          expect.objectContaining({
            success: true,
            method: 'paypal',
            amount: 25.50
          })
        );
      }, { timeout: 2000 });
    });
  });
});
