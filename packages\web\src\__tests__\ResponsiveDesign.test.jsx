import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import { BrowserRouter } from 'react-router-dom';
import { CartProvider, LanguageProvider, AuthProvider } from '@kivu-smartfarm/shared';
import Navigation from '../components/Navigation';
import CartSidebar from '../components/CartSidebar';

const renderWithProviders = (component) => {
  return render(
    <BrowserRouter>
      <LanguageProvider>
        <AuthProvider>
          <CartProvider>
            {component}
          </CartProvider>
        </AuthProvider>
      </LanguageProvider>
    </BrowserRouter>
  );
};

// Helper function to simulate different screen sizes
const setViewport = (width, height) => {
  Object.defineProperty(window, 'innerWidth', {
    writable: true,
    configurable: true,
    value: width,
  });
  Object.defineProperty(window, 'innerHeight', {
    writable: true,
    configurable: true,
    value: height,
  });
  window.dispatchEvent(new Event('resize'));
};

describe('Responsive Design', () => {
  beforeEach(() => {
    // Reset to desktop size
    setViewport(1024, 768);
  });

  describe('Navigation Component', () => {
    test('should show desktop navigation on large screens', () => {
      setViewport(1024, 768);
      renderWithProviders(<Navigation />);

      // Desktop navigation should be visible
      expect(screen.getByText('Kivu SMARTFARM')).toBeInTheDocument();
      
      // Mobile menu button should be hidden (has md:hidden class)
      const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
      expect(mobileMenuButton).toHaveClass('md:hidden');
    });

    test('should show mobile navigation on small screens', () => {
      setViewport(375, 667); // iPhone SE size
      renderWithProviders(<Navigation />);

      // Mobile menu button should be visible
      const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
      expect(mobileMenuButton).toBeInTheDocument();
      
      // Desktop navigation items should be hidden on mobile
      const desktopNav = screen.getByText('Home').closest('div');
      expect(desktopNav).toHaveClass('hidden', 'md:flex');
    });

    test('should toggle mobile menu correctly', () => {
      setViewport(375, 667);
      renderWithProviders(<Navigation />);

      const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
      
      // Mobile menu should be closed initially
      expect(screen.queryByText('English')).not.toBeInTheDocument();
      
      // Open mobile menu
      fireEvent.click(mobileMenuButton);
      
      // Mobile menu items should be visible
      expect(screen.getByText('English')).toBeInTheDocument();
      expect(screen.getByText('Shopping Cart')).toBeInTheDocument();
    });

    test('should display cart icon in both desktop and mobile', () => {
      // Test desktop
      setViewport(1024, 768);
      renderWithProviders(<Navigation />);
      
      let cartButtons = screen.getAllByRole('button');
      const hasCartIcon = cartButtons.some(button => 
        button.querySelector('svg') && 
        button.getAttribute('class')?.includes('relative')
      );
      expect(hasCartIcon).toBe(true);

      // Test mobile
      setViewport(375, 667);
      renderWithProviders(<Navigation />);
      
      // Open mobile menu to see cart icon
      const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
      fireEvent.click(mobileMenuButton);
      
      expect(screen.getByText('Shopping Cart')).toBeInTheDocument();
    });
  });

  describe('Cart Sidebar', () => {
    test('should be responsive on different screen sizes', () => {
      const { rerender } = renderWithProviders(<CartSidebar />);

      // Test on mobile
      setViewport(375, 667);
      rerender(
        <BrowserRouter>
          <LanguageProvider>
            <AuthProvider>
              <CartProvider>
                <CartSidebar />
              </CartProvider>
            </AuthProvider>
          </LanguageProvider>
        </BrowserRouter>
      );

      // Cart sidebar should have responsive classes
      // The sidebar uses max-w-md which is responsive
      
      // Test on tablet
      setViewport(768, 1024);
      rerender(
        <BrowserRouter>
          <LanguageProvider>
            <AuthProvider>
              <CartProvider>
                <CartSidebar />
              </CartProvider>
            </AuthProvider>
          </LanguageProvider>
        </BrowserRouter>
      );

      // Test on desktop
      setViewport(1440, 900);
      rerender(
        <BrowserRouter>
          <LanguageProvider>
            <AuthProvider>
              <CartProvider>
                <CartSidebar />
              </CartProvider>
            </AuthProvider>
          </LanguageProvider>
        </BrowserRouter>
      );
    });
  });

  describe('Breakpoint Behavior', () => {
    const breakpoints = [
      { name: 'mobile', width: 375, height: 667 },
      { name: 'tablet', width: 768, height: 1024 },
      { name: 'desktop', width: 1024, height: 768 },
      { name: 'large-desktop', width: 1440, height: 900 },
    ];

    breakpoints.forEach(({ name, width, height }) => {
      test(`should render correctly on ${name} (${width}x${height})`, () => {
        setViewport(width, height);
        renderWithProviders(<Navigation />);

        // Basic elements should always be present
        expect(screen.getByText('Kivu SMARTFARM')).toBeInTheDocument();
        
        // Check if mobile menu behavior is correct
        if (width < 768) {
          // Mobile: menu button should be visible
          const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
          expect(mobileMenuButton).toBeInTheDocument();
        } else {
          // Desktop/Tablet: navigation items should be visible
          expect(screen.getByText('Home')).toBeInTheDocument();
          expect(screen.getByText('Marketplace')).toBeInTheDocument();
        }
      });
    });
  });

  describe('Touch and Click Interactions', () => {
    test('should handle touch events on mobile', () => {
      setViewport(375, 667);
      renderWithProviders(<Navigation />);

      const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
      
      // Simulate touch event
      fireEvent.touchStart(mobileMenuButton);
      fireEvent.touchEnd(mobileMenuButton);
      fireEvent.click(mobileMenuButton);
      
      // Menu should open
      expect(screen.getByText('Shopping Cart')).toBeInTheDocument();
    });

    test('should handle hover states on desktop', () => {
      setViewport(1024, 768);
      renderWithProviders(<Navigation />);

      const homeLink = screen.getByText('Home');
      
      // Simulate hover
      fireEvent.mouseEnter(homeLink);
      fireEvent.mouseLeave(homeLink);
      
      // Link should still be functional
      expect(homeLink).toBeInTheDocument();
    });
  });

  describe('Text and Content Scaling', () => {
    test('should maintain readability across screen sizes', () => {
      const sizes = [375, 768, 1024, 1440];
      
      sizes.forEach(width => {
        setViewport(width, 768);
        renderWithProviders(<Navigation />);
        
        const logo = screen.getByText('Kivu SMARTFARM');
        expect(logo).toBeInTheDocument();
        
        // Logo should have appropriate text size classes
        expect(logo).toHaveClass('font-bold', 'text-xl');
      });
    });

    test('should handle long text gracefully', () => {
      setViewport(375, 667); // Small mobile screen
      renderWithProviders(<Navigation />);

      // Open mobile menu
      const mobileMenuButton = screen.getByRole('button', { name: /menu/i });
      fireEvent.click(mobileMenuButton);

      // Language selector should be present and functional
      expect(screen.getByText('Language')).toBeInTheDocument();
    });
  });
});
