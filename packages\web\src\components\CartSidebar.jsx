import React, { useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { X, Plus, Minus, Trash2, ShoppingBag } from 'lucide-react';
import { Button } from '@ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@ui/card';
import { Badge } from '@ui/badge';
import { useCart } from '@kivu-smartfarm/shared';
import { useNavigate } from 'react-router-dom';

const CartSidebar = () => {
  const {
    cartItems,
    isCartOpen,
    setIsCartOpen,
    updateQuantity,
    removeFromCart,
    clearCart,
    getTotalPrice,
    getTotalItems
  } = useCart();

  const navigate = useNavigate();

  // Prevent body scroll when cart is open
  useEffect(() => {
    if (isCartOpen) {
      document.body.classList.add('cart-open');
    } else {
      document.body.classList.remove('cart-open');
    }

    // Cleanup on unmount
    return () => {
      document.body.classList.remove('cart-open');
    };
  }, [isCartOpen]);

  const handleCheckout = () => {
    setIsCartOpen(false);
    navigate('/checkout');
  };

  const formatPrice = (price) => {
    return `$${price.toFixed(2)}`;
  };

  return (
    <AnimatePresence>
      {isCartOpen && (
        <div className="cart-container">
          {/* Backdrop */}
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={() => setIsCartOpen(false)}
            className="cart-overlay"
          />

          {/* Sidebar */}
          <motion.div
            initial={{ x: '100%' }}
            animate={{ x: 0 }}
            exit={{ x: '100%' }}
            transition={{ type: 'spring', damping: 25, stiffness: 200 }}
            className="cart-sidebar w-full max-w-md flex flex-col"
          >
            {/* Header */}
            <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 bg-white">
              <h2 className="text-lg sm:text-xl font-semibold flex items-center gap-2 text-gray-900">
                <ShoppingBag className="w-5 h-5 text-green-600" />
                Shopping Cart
                {getTotalItems() > 0 && (
                  <Badge variant="secondary" className="ml-2 bg-green-100 text-green-800 border-green-200">
                    {getTotalItems()} items
                  </Badge>
                )}
              </h2>
              <Button
                variant="ghost"
                size="sm"
                onClick={() => setIsCartOpen(false)}
                className="p-2 hover:bg-gray-100 text-gray-500 hover:text-gray-700"
              >
                <X className="w-5 h-5" />
              </Button>
            </div>

            {/* Cart Items */}
            <div className="flex-1 overflow-y-auto p-4 sm:p-6 bg-gray-50 min-h-[120px] max-h-[50vh] sm:max-h-[60vh]">
              {cartItems.length === 0 ? (
                <div className="flex flex-col items-center justify-center h-full text-center bg-white rounded-lg p-8 shadow-sm">
                  <ShoppingBag className="w-16 h-16 text-gray-300 mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Your cart is empty</h3>
                  <p className="text-gray-600 mb-6">Add some products to get started</p>
                  <Button
                    onClick={() => {
                      setIsCartOpen(false);
                      navigate('/marketplace');
                    }}
                    className="gradient-bg shadow-md hover:shadow-lg transition-shadow"
                  >
                    Browse Products
                  </Button>
                </div>
              ) : (
                <div className="space-y-4">
                  {cartItems.map((item) => (
                    <Card key={item.id} className="overflow-hidden bg-white shadow-md border border-gray-200 hover:shadow-lg transition-shadow">
                      <CardContent className="p-3 sm:p-4">
                        <div className="flex gap-3 sm:gap-4 items-center">
                          <img
                            src={item.image || 'https://images.unsplash.com/photo-1626347472833-cc1b65b9b470'}
                            alt={item.name}
                            className="w-12 h-12 sm:w-16 sm:h-16 object-cover rounded-lg border border-gray-200 flex-shrink-0"
                          />
                          <div className="flex-1 min-w-0">
                            <h4 className="font-medium text-sm truncate text-gray-900">{item.name}</h4>
                            {item.farmer && <p className="text-xs text-gray-600 truncate">{item.farmer}</p>}
                            <p className="text-xs sm:text-sm font-semibold text-green-600 mt-1">
                              {formatPrice(item.price)} per kg
                            </p>
                          </div>
                          <Button
                            variant="ghost"
                            size="icon"
                            onClick={() => removeFromCart(item.id)}
                            className="h-8 w-8 p-0 text-red-500 hover:text-red-700 hover:bg-red-50 rounded-full ml-2"
                            aria-label="Remove item"
                          >
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                        <div className="flex items-center justify-between mt-3 pt-2 border-t border-gray-100">
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => updateQuantity(item.id, item.quantity - 1)}
                              className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                              aria-label="Decrease quantity"
                            >
                              <Minus className="w-3 h-3 text-gray-600" />
                            </Button>
                            <span className="w-8 text-center text-sm font-medium text-gray-900 bg-gray-50 rounded px-2 py-1">
                              {item.quantity}
                            </span>
                            <Button
                              variant="outline"
                              size="icon"
                              onClick={() => updateQuantity(item.id, item.quantity + 1)}
                              className="h-8 w-8 p-0 border-gray-300 hover:border-gray-400 hover:bg-gray-50"
                              aria-label="Increase quantity"
                            >
                              <Plus className="w-3 h-3 text-gray-600" />
                            </Button>
                          </div>
                          <span className="font-semibold text-sm text-gray-900 bg-green-50 px-2 py-1 rounded">
                            {formatPrice(item.price * item.quantity)}
                          </span>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              )}
            </div>

            {/* Footer */}
            {cartItems.length > 0 && (
              <div className="border-t border-gray-200 p-4 sm:p-6 space-y-4 bg-white shadow-lg">
                <div className="flex justify-between items-center p-3 sm:p-4 bg-gray-50 rounded-lg border border-gray-200">
                  <span className="text-base sm:text-lg font-normal text-gray-800">Total:</span>
                  <span className="text-lg sm:text-xl font-bold text-green-600 bg-white px-3 py-1 rounded border border-green-200">
                    {formatPrice(getTotalPrice())}
                  </span>
                </div>
                <div className="space-y-2 sm:space-y-3">
                  <Button
                    onClick={handleCheckout}
                    className="w-full gradient-bg shadow-md hover:shadow-lg transition-all duration-200 font-medium"
                    size="lg"
                  >
                    Proceed to Checkout
                  </Button>
                  <Button
                    variant="outline"
                    onClick={clearCart}
                    className="w-full border-gray-300 text-gray-700 hover:bg-gray-50 hover:border-gray-400 transition-colors"
                    size="sm"
                  >
                    Clear Cart
                  </Button>
                </div>
              </div>
            )}
          </motion.div>
        </div>
      )}
    </AnimatePresence>
  );
};

export default CartSidebar;
