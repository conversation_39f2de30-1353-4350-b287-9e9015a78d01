@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --popover: 0 0% 100%;
  --popover-foreground: 222.2 84% 4.9%;
  --primary: 142 76% 36%;
  --primary-foreground: 355.7 100% 97.3%;
  --secondary: 210 40% 98%;
  --secondary-foreground: 222.2 84% 4.9%;
  --muted: 210 40% 98%;
  --muted-foreground: 215.4 16.3% 46.9%;
  --accent: 210 40% 98%;
  --accent-foreground: 222.2 84% 4.9%;
  --destructive: 0 84.2% 60.2%;
  --destructive-foreground: 210 40% 98%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 142 76% 36%;
  --radius: 0.5rem;
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --card: 222.2 84% 4.9%;
  --card-foreground: 210 40% 98%;
  --popover: 222.2 84% 4.9%;
  --popover-foreground: 210 40% 98%;
  --primary: 142 76% 36%;
  --primary-foreground: 355.7 100% 97.3%;
  --secondary: 217.2 32.6% 17.5%;
  --secondary-foreground: 210 40% 98%;
  --muted: 217.2 32.6% 17.5%;
  --muted-foreground: 215 20.2% 65.1%;
  --accent: 217.2 32.6% 17.5%;
  --accent-foreground: 210 40% 98%;
  --destructive: 0 62.8% 30.6%;
  --destructive-foreground: 210 40% 98%;
  --border: 217.2 32.6% 17.5%;
  --input: 217.2 32.6% 17.5%;
  --ring: 142 76% 36%;
}

* {
  border-color: hsl(var(--border));
}

body {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.gradient-bg {
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 50%, #15803d 100%);
}

.gradient-text {
  background: linear-gradient(135deg, #22c55e, #16a34a);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.card-hover {
  transition: all 0.3s ease;
}

.card-hover:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(34, 197, 94, 0.2);
}

.animate-float {
  animation: float 6s ease-in-out infinite;
}

@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

.animate-pulse-slow {
  animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.hero-pattern {
  background-image: 
    radial-gradient(circle at 25% 25%, rgba(34, 197, 94, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 75% 75%, rgba(22, 163, 74, 0.1) 0%, transparent 50%);
}

.feature-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.stats-card {
  background: linear-gradient(135deg, rgba(34, 197, 94, 0.1), rgba(22, 163, 74, 0.05));
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.language-selector {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
}

/* Gradient Backgrounds */
.gradient-bg {
  background: linear-gradient(135deg, #059669 0%, #10b981 100%);
  transition: all 0.3s ease;
}

.gradient-bg:hover {
  background: linear-gradient(135deg, #047857 0%, #059669 100%);
  transform: translateY(-2px);
}

/* Chatbot specific styles */
.react-chatbot-kit-chat-container {
  width: 100% !important;
  height: 100% !important;
  background: #f8fafc;
}

.react-chatbot-kit-chat-inner-container {
  height: 100% !important;
  background: transparent;
}

.react-chatbot-kit-chat-header {
  display: none;
}

.react-chatbot-kit-chat-message-container {
  padding: 16px !important;
  scrollbar-width: thin;
  scrollbar-color: #e2e8f0 transparent;
}

.react-chatbot-kit-chat-message-container::-webkit-scrollbar {
  width: 6px;
}

.react-chatbot-kit-chat-message-container::-webkit-scrollbar-track {
  background: transparent;
}

.react-chatbot-kit-chat-message-container::-webkit-scrollbar-thumb {
  background-color: #e2e8f0;
  border-radius: 20px;
}

.react-chatbot-kit-chat-input-container {
  position: absolute !important;
  bottom: 0 !important;
  width: 100% !important;
  padding: 16px !important;
  background: white !important;
  border-top: 1px solid #e5e7eb !important;
}

.react-chatbot-kit-chat-input {
  padding: 12px 16px !important;
  border-radius: 24px !important;
  border: 2px solid #e5e7eb !important;
  font-size: 15px !important;
  transition: all 0.2s ease !important;
  width: calc(100% - 70px) !important;
}

.react-chatbot-kit-chat-input:focus {
  outline: none !important;
  border-color: #22c55e !important;
  box-shadow: 0 0 0 2px rgba(34, 197, 94, 0.1) !important;
}

.react-chatbot-kit-chat-btn-send {
  width: 42px !important;
  height: 42px !important;
  border-radius: 50% !important;
  padding: 0 !important;
  background: linear-gradient(135deg, #22c55e 0%, #16a34a 100%) !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

.react-chatbot-kit-chat-btn-send:hover {
  transform: scale(1.05) !important;
}

.react-chatbot-kit-chat-btn-send svg {
  width: 20px !important;
  height: 20px !important;
  fill: white !important;
}

@keyframes bounce-subtle {
  0%, 100% { transform: translateY(0); }
  50% { transform: translateY(-4px); }
}

.animate-bounce-subtle {
  animation: bounce-subtle 2s ease-in-out infinite;
}

/* Cart overlay and sidebar styles */
.cart-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9998 !important;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  -webkit-backdrop-filter: blur(4px);
}

.cart-sidebar {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 9999 !important;
  background: white;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05);
  border-left: 1px solid #e5e7eb;
}

/* Ensure cart is always on top */
.cart-container {
  z-index: 9999 !important;
}

/* Prevent body scroll when cart is open */
body.cart-open {
  overflow: hidden;
}

/* Custom scrollbar for cart */
.cart-sidebar ::-webkit-scrollbar {
  width: 6px;
}

.cart-sidebar ::-webkit-scrollbar-track {
  background: #f1f5f9;
}

.cart-sidebar ::-webkit-scrollbar-thumb {
  background: #cbd5e1;
  border-radius: 3px;
}

.cart-sidebar ::-webkit-scrollbar-thumb:hover {
  background: #94a3b8;
}
